<template>
  <ul class="wave-menu" :style="{ width: width }">
    <li v-for="index in num" :key="index"></li>
  </ul>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  width?: string,
  num?: number
}>(), {
  width: '300px',
  num: 40
})
</script>

<style lang="less" scoped>
.wave-menu {
  border-radius: 50px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0;
  cursor: pointer;
  transition: ease 0.2s;
  position: relative;
  background: transparent;
}

.wave-menu li {
  list-style: none;
  height: 10px;
  width: 2px;
  border-radius: 10px;
  background: #fff;
  margin: 0 2px;
  padding: 0;
  animation-name: wave1;
  animation-duration: 0.3s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  transition: ease 0.2s;
}

.wave-menu li:nth-child(10n + 1) {
  animation-name: wave1;
}

.wave-menu li:nth-child(10n + 2) {
  animation-name: wave2;
  animation-delay: 0.2s;
}

.wave-menu li:nth-child(10n + 3) {
  animation-name: wave3;
  animation-delay: 0.23s;
  animation-duration: 0.4s;
}

.wave-menu li:nth-child(10n + 4) {
  animation-name: wave4;
  animation-delay: 0.1s;
  animation-duration: 0.3s;
}

.wave-menu li:nth-child(10n + 5) {
  animation-delay: 0.5s;
}

.wave-menu li:nth-child(10n + 6) {
  animation-name: wave2;
  animation-duration: 0.5s;
}

.wave-menu li:nth-child(10n + 8) {
  animation-name: wave4;
  animation-delay: 0.4s;
  animation-duration: 0.25s;
}

.wave-menu li:nth-child(10n + 9) {
  animation-name: wave3;
  animation-delay: 0.15s;
}

@keyframes wave1 {
  from {
    transform: scaleY(1);
  }

  to {
    transform: scaleY(0.5);
  }
}

@keyframes wave2 {
  from {
    transform: scaleY(0.3);
  }

  to {
    transform: scaleY(0.6);
  }
}

@keyframes wave3 {
  from {
    transform: scaleY(0.6);
  }

  to {
    transform: scaleY(0.8);
  }
}

@keyframes wave4 {
  from {
    transform: scaleY(0.2);
  }

  to {
    transform: scaleY(0.5);
  }
}
</style>
