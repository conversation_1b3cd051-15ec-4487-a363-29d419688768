<template>
  <van-popup v-model:show="isShow" ref="vanPopupRef" position="bottom" round safe-area-inset-bottom :overlay="isOverlay"
    :close-on-click-overlay="false" :style="{ display: isOverlay ? 'block' : 'none' }">
    <div class="popWrapper" ref="popContentRef" :style="{ 'height': popHeight + 'px' }">
      <div class="popTitle">选择人员</div>
      <div class="popLoading" v-if="isLoading">
        <van-loading v-if="isLoading" type="spinner" />
      </div>
      <iframe ref="iframeRef" :src="iframeSrc" width="100%" @load="iframeOnload"></iframe>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { getToken } from '@/utils/token'
const emit = defineEmits(['select'])

const vanPopupRef = ref(null)
const isShow = ref(true)
const popHeight = window.innerHeight * 0.8
const isLoading = ref(true)
const iframeRef = ref(null)
const popContentRef = ref(null)
const isOverlay = ref(false)
const iframeSrc = `${import.meta.env.VITE_IFRAME_DOMAIN}/newapp/pages/common/choice/index?type=1000&locale=zh&token=${getToken()}`

async function open() {
  isOverlay.value = true
  isShow.value = true
  // 打开弹窗时，把 iframe 移进弹窗容器
  await nextTick()
  if (!popContentRef.value.querySelector('iframe')) {
    popContentRef.value.appendChild(iframeRef.value)
    iframeRef.value.style.visibility = 'visible'
    iframeRef.value.style.position = 'static'
  }
}

function iframeOnload() {
  isLoading.value = false
  if (!isOverlay.value) {
    isShow.value = false
  }
}

function close() {
  isShow.value = false
}

function handleMessage(event) {
  console.log(event)
  if (!event || !event.data.type) return

  console.log('收到消息: ', event.data, '来源: ', event.origin)

  return emit('select', event.data)
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
})

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})

defineExpose({
  open,
  close
})
</script>

<style scoped>
iframe {
  margin: 0;
  padding: 0;
  display: block;
  border: 0;
  height: calc(100% - 40px);
}

.popWrapper {
  background: #fff;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.popTitle {
  padding-top: 12px;
  font-size: 18px;
  text-align: center;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  position: relative;
}

.popLoading {
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f6f8;
}
</style>
