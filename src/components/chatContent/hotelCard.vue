<template>
  <div class="hotel-card">
    <div class="d-flex">
      <div class="hotel-image">
        <img :src="hotel_data.headPicture" />
        <span class="hotel-tag">⭐ 推荐</span>
        <span class="hotel-star">{{ hotel_data.starTxt }}</span>
      </div>
      <div class="hotel-info">
        <span class="hotel-name">{{ hotel_data.hotelName }}</span>
        <div class="mt6 d-flex-align">
          <span class="hotel-score">{{ hotel_data.praiseScore }}</span>
          <span class="hotel-desc">{{ hotel_data.Detail.Address }}</span>
        </div>
        <div class="mt4 nowrap">
          <span class="hotel-fac" v-for="(item, index) in hotel_data.facilities" :key="index">{{ item }}</span>
        </div>
      </div>
    </div>
    <div class="hotel-room">
      <div class="room-policy">
        <div class="d-flex-between"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  sid: String,
  msgId: String,
  message: Object,
  latestMsg: Boolean,
  type: String,
  hotel_data: Object
})

console.log(props.hotel_data)
</script>

<style lang="less" scoped>
.hotel-card {
  margin-top: 0.75em;
  min-height: 170px;
  background: #fff;
  border-radius: 15px;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);

  .hotel-image {
    flex-shrink: 0;
    width: 92px;
    height: 92px;
    border-top-left-radius: 15px;
    position: relative;

    img {
      border-top-left-radius: 15px;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .hotel-tag {
      position: absolute;
      left: 0;
      top: 0;
      background: linear-gradient(276deg, #fec876 0%, #ffe2b5 100%);
      border-radius: 15px 0 10px 0;
      font-weight: 500;
      font-size: 9px;
      color: #875533;
      line-height: 10px;
      padding: 5px 5px 4px;
    }

    .hotel-star {
      position: absolute;
      right: 5px;
      bottom: 5px;
      background: linear-gradient(#feddab 0%, #ffecd0 100%);
      border-radius: 3px;
      font-weight: 500;
      font-size: 11px;
      color: #885634;
      line-height: 12px;
      padding: 2px;
    }
  }

  .hotel-info {
    padding: 6px 5px 0;
    flex: 1;
    min-width: 0;
    border-bottom: 1px solid #e4e5e8;

    .hotel-name {
      font-weight: 600;
      font-size: 14px;
      color: #000;
      line-height: 1.2;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      max-height: calc(1.2em * 2);
    }

    .hotel-score {
      flex-shrink: 0;
      white-space: nowrap;
      background: #4e6cfe;
      border-radius: 2px;
      font-weight: 600;
      font-size: 10px;
      color: #fff;
      line-height: 12px;
      padding: 1px 2px;
    }

    .hotel-desc {
      flex: 1;
      min-width: 0;
      margin-left: 5px;
      font-weight: 400;
      font-size: 13px;
      color: #000;
      line-height: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .hotel-fac {
      margin-right: 5px;
      border-radius: 3px;
      border: 1px solid #b3b3b3;
      font-weight: 400;
      font-size: 9px;
      color: #727272;
      line-height: 10px;
      padding: 3px 2px;

      &:last-of-type {
        margin-right: 5px;
      }
    }
  }

  .hotel-room {
    padding: 5px 6px 10px;

    .room-policy {
      background: #fff4ee;
      border-radius: 11px;
      padding: 5px 7px 7px;
    }
  }
}
</style>
