<template>
  <div class="input-container" :class="{ expand: isVoiceRecording }">
    <div class="input-wrapper">
      <div v-show="sendMode !== 'voice'" class="input-box-bg">
        <div class="input-box">
          <textarea class="input-textarea" :placeholder="inputPlaceholder" v-model="inputMessage" enterkeyhint="send"
            :rows="1" resize="none" ref="inputRef" @focus="handleFocus" @blur="handleBlur"
            @keydown.enter="handleEnter($event)" @input="handleInput"></textarea>
          <div class="chat-icons">
            <div class="icon-voice" @click="() => changeSendMode('voice')"></div>
            <div v-if="loading" class="icon-stop" @click="() => stopGenerating()"></div>
            <div v-else class="icon-send" :class="{ active: isSendEnable }" @click="() => sendMessage()"></div>
          </div>
        </div>
      </div>
      <div v-show="sendMode === 'voice'">
        <div class="input-box-bg d-flex" :class="{ 'opacity-0': isVoiceRecording }">
          <voiceButton class="voice-btn" v-if="sendMode === 'voice'" @recordingChange="recordingChange"
            @recordingFinish="recordingFinish" @stopGenerating="stopGenerating" />
          <div v-if="!isVoiceRecording" class="chat-icons">
            <div class="icon-keyboard" @click="() => changeSendMode('input')"></div>
            <div v-if="loading" class="icon-stop" @click="() => stopGenerating()"></div>
            <div v-else :class="{ active: isSendEnable }" class="icon-send"></div>
          </div>
        </div>
        <div class="recoding-container" v-if="isVoiceRecording">
          <div class="send-text">松手发送，上移取消</div>
          <wave-loading :num="20" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { DEFAULT_HOLDER } from '@/constants'
import Recorder from 'js-audio-recorder'
import voiceButton from './voiceButton.vue'
import waveLoading from './waveLoading.vue'
import { showToast } from 'vant'

const props = defineProps({
  inputPlaceholder: String,
  loading: Boolean
})

const emit = defineEmits(['stopGenerating', 'sendMessage', 'changeFocus'])

const sendMode = ref('input') // 发送模式，input 输入，voice 语音
const isVoiceRecording = ref(false) // 是否正在录音
const inputMessage = ref('')

const recordingChange = (isRecording) => {
  isVoiceRecording.value = isRecording
}

const recordingFinish = (text) => {
  emit('sendMessage', text, sendMode.value)
  inputMessage.value = ''
}

const sendMessage = (text) => {
  text =
    text ||
    inputMessage.value ||
    (props.inputPlaceholder === DEFAULT_HOLDER() ? '' : props.inputPlaceholder)

  if (text) {
    emit('sendMessage', text, sendMode.value)
    inputMessage.value = ''
  }
}

const checkRecordPermission = async () => {
  if (!('permissions' in navigator)) {
    return false
  }

  const permissionStatus = await navigator.permissions.query({
    name: 'microphone'
  })

  return permissionStatus.state === 'granted'
}

const changeSendMode = async (mode) => {
  if (mode !== 'voice') {
    sendMode.value = mode
    return
  }
  try {
    const hasPermission = await checkRecordPermission()
    if (!hasPermission) {
      await Recorder.getPermission()
    }
    sendMode.value = mode
  } catch (e) {
    if (e && e.message && e.message.includes('getUserMedia')) {
      showToast(`浏览器获取麦克风权限失败(请检查是否访问的是https)`)
      return
    }
    showToast('需要授权麦克风权限，重新进入再次授权询问。')
  }
}

const setInputMessage = (val) => {
  inputMessage.value = `${val} ${inputMessage.value}`
}

const clearInputMessage = () => {
  inputMessage.value = ''
}

defineExpose({
  setInputMessage,
  clearInputMessage
})

const isFocus = ref(false)
const inputRef = ref()

const handleFocus = () => {
  isFocus.value = true
}

const handleBlur = () => {
  isFocus.value = false
}

watch(
  () => isFocus.value,
  newVal => {
    if (!newVal) return
    emit('changeFocus', newVal)
  },
)

const isSendEnable = computed(() => {
  return inputMessage.value
})

const handleEnter = (e) => {
  if (e instanceof KeyboardEvent && e.code === 'Enter') {
    if (!e.shiftKey) {
      e.preventDefault()
      sendMessage()
      // 收起键盘
      if (inputRef.value) {
        inputRef.value.blur()
      }
    }
  }
}

const handleInput = () => {
  inputRef.value.style.height = 'auto'
  inputRef.value.style.height = Math.min(100, inputRef.value.scrollHeight) + 'px'
}

const stopGenerating = () => {
  emit('stopGenerating')
}
</script>

<style lang="less" scoped>
.input-container {
  padding: 0 16px;
  padding-top: 10px;
  padding-bottom: 20px;
  background: url('https://m.elongstatic.com/mall-v2/mp-deeptrip/voicebg2.png') no-repeat center center;
  background-size: 0 0;
  background-color: #fff;
  box-sizing: border-box;
}

.input-container.expand {
  height: 240px;
  background-size: 100% 100%;
  background-color: transparent;
}

.input-wrapper {
  position: relative;
  min-height: 40px;
}

.input-box-bg {
  box-shadow: 0px 2px 7px 0px rgba(0, 0, 0, 0.15);
  border-radius: 20px;
  padding: 0 10px;
  box-sizing: border-box;
}

.opacity-0 {
  opacity: 0;
}

.input-box {
  opacity: 0.85;
  transition: all 0.3s;
  display: flex;
  box-sizing: border-box;
}

.input-textarea {
  background: transparent;
  border: none;
  box-shadow: none;
  outline: none;
  padding: 8px 10px;
  font-size: 16px;
  line-height: 24px;
  color: #000;
  flex: 1;
  resize: none;
  box-sizing: border-box;
  size: auto;
  min-height: 40px;
  max-height: 100px;
}

.input-textarea::placeholder {
  color: #999;
  font-weight: 400;
  font-size: 16px;
}

.chat-icons {
  display: flex;
  align-items: center;
  min-height: 40px;

  >div {
    width: 24px;
    height: 24px;

    &:first-child {
      margin-left: 10px;
    }

    &:not(:last-child) {
      margin-right: 12px;
    }
  }
}

.icon-voice {
  background: url('https://m.elongstatic.com/mall-v2/mp-deeptrip/icons/icon-voice.png') no-repeat center center;
  background-size: contain;
}

.icon-stop {
  background: url('../assets/icons/stop.png') no-repeat center center;
  background-size: contain;

  &:hover {
    background: url('../assets/icons/stop-clicked.png') no-repeat center center;
    background-size: contain;
  }
}

.icon-send {
  border-radius: 50%;
  background: url('https://imgdttrip.40017.cn/h5/V3/common/chat_send_btn.png') no-repeat center center;
  background-size: contain;

  &.active {
    background: url('https://m.elongstatic.com/mall-v2/mp-deeptrip/localeImages/send-icon2-active.png') no-repeat center center;
    background-size: contain;
    box-shadow: 0px 2px 5px 0px rgba(23, 206, 146, 0.25);
  }
}

.voice-btn {
  flex: 1;
}

.icon-keyboard {
  background: url('https://m.elongstatic.com/mall-v2/mp-deeptrip/icons/icon-keyboard.png') no-repeat center center;
  background-size: contain;
}

.recoding-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
}

.send-text {
  font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
  font-size: 14px;
  color: #fff;
  margin-bottom: -10px;
  text-shadow: 0px 4px 15px #14ca68;
}
</style>
