<template>
  <div class="voice-button" :class="{ recording: isRecording, cancel: recordingMode === 'cancel' }"
    @touchstart.prevent="startRecording" @touchmove.prevent="handleTouchMove" @touchend.prevent="handleTouchEnd"
    @contextmenu="event => event.preventDefault()">
    <div class="button-text" v-show="!isRecording && recordingMode === '' && !audioLoading">按住说话</div>
    <div class="button-text" v-show="audioLoading">启动中...</div>
    <div class="button-text" v-show="recordingMode === 'recognizing'">识别中...</div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import * as lamejs from '@breezystack/lamejs'
import Recorder from 'js-audio-recorder'
import { convertMp3ToText } from '@/api'
import { useI18n } from '@tci18n/vue3'
import { showToast } from 'vant'

const OAI_SAMPLE_RATE = 16000
const realMaxRecordingDuration = 59 // 60秒
const maxRecordingDuration = 60 // 60秒
const cancelThreshold = 100 // 上移取消的阈值（像素）

const recorder = new Recorder({
  sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
  sampleRate: OAI_SAMPLE_RATE, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
  numChannels: 1, // 声道，支持 1 或 2， 默认是1
  // compiling: false,(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
})

recorder.onprogress = () => { }

const emit = defineEmits([
  'recordingChange',
  'recordingFinish',
  'stopGenerating'
])

const stat = ref({ duration: 0, fileSize: 0, vol: 0 })
const startTime = ref(0)
const st = ref({ start: 0, isGo: false })

const recordingMode = ref('') // recording, recognizing，cancel，finish
const pressCancelTimer = ref()
const recognizeText = ref('')

const startY = ref(0)
const currentY = ref(0)
const startClickTime = ref(0)
const audioLoading = ref(false)
const isRecording = ref(false)
const shouldCancel = ref(false)

const startRecording = (event) => {
  event.preventDefault()
  startClickTime.value = Date.now()
  startY.value = event instanceof MouseEvent ? event.clientY : event.touches[0].clientY
  currentY.value = startY.value
  shouldCancel.value = false

  console.log('Long press detected!')
  recorder.onprogress = function (params) {
    drawWave()
    stat.value = {
      duration: params.duration,
      fileSize: params.fileSize,
      vol: params.vol
    }

    // 检查录音时长是否超过最大时长
    if (stat.value.duration >= realMaxRecordingDuration) {
      showToast(`语音时长超过${maxRecordingDuration}秒`)
      resetRecorder()
      exportMp3()
    }
  }
  // 重置录音数据
  audioLoading.value = true
  recorder
    .start()
    .then(() => {
      audioLoading.value = false
      if (startClickTime.value === 0) return
      startTime.value = Date.now()
      st.value.start = 1
      st.value.isGo = true
      isRecording.value = true
      recordingMode.value = 'recording'

      emit('recordingChange', true)
    })
    .catch(e => {
      audioLoading.value = false
      if (startClickTime.value === 0) return
      isRecording.value = false
      recordingMode.value = ''
      emit('recordingChange', false)
    })
}

const waveData = ref([])
const num = 30
const drawWave = () => {
  // 获取音频分析数据
  // 设置波形图的最大和最小高度范围
  const dataArray = recorder.getRecordAnalyseData()
  // 将数据分组平均，减少到 10 个值
  const groupSize = Math.floor(dataArray.length / num)
  // 绘制声纹
  waveData.value = []
  for (let i = 0; i < num; i++) {
    let sum = 0
    for (let j = 0; j < groupSize; j++) {
      sum += (dataArray[i * groupSize + j] / 128 * 1)
    }
    waveData.value.push({
      height: Math.min(sum / groupSize, 2)
    })
  }
}

const handleTouchMove = (event) => {
  event.preventDefault() // 阻止默认滚动
  if (!isRecording.value) return

  currentY.value = event.touches[0].clientY
  const deltaY = startY.value - currentY.value // 向上移动时deltaY为正

  if (deltaY > cancelThreshold) {
    shouldCancel.value = true
    recordingMode.value = 'cancel'
  } else {
    shouldCancel.value = false
    recordingMode.value = 'recording'
  }
}

const handleTouchEnd = () => {
  if (shouldCancel.value) {
    cancelRecording()
  } else {
    stopRecording()
  }
}

const recorderStop = () => {
  st.value.start = 0
  recorder.stop()
  recorder.stopPlay()
}

const stopRecording = async (event) => {
  startClickTime.value = 0
  if (!isRecording.value) {
    resetRecorder()
    return
  }

  const recordingDuration = Date.now() - startTime.value // 计算录音时长（毫秒）

  // 如果录音时长少于1.5秒（1500毫秒）
  if (recordingDuration < 1500) {
    resetRecorder()
    showToast('说话时间过短')
    return
  }

  if (recordingMode.value === 'cancel') {
    cancelRecording()
    return
  }

  await resetRecorder()
  isRecording.value = false
  recordingMode.value = 'recognizing'
  emit('recordingChange', false)

  stat.value = { duration: 0, fileSize: 0, vol: 0 }
  exportMp3()
}

const cancelRecording = () => {
  console.log('Long press cancel!', isRecording.value)
  if (!isRecording.value) return
  recordingMode.value = 'cancel'

  pressCancelTimer.value = setTimeout(() => {
    resetRecorder()
    shouldCancel.value = false
  }, 200)
}

// 重置recorder
const resetRecorder = async () => {
  try {
    // 停止录音
    await recorder.stop()
    // 手动移除 onprogress 监听器
    recorder.onprogress = () => { }
  } catch (error) {
    console.error('resetRecorder 错误:', error)
  }

  // 访问私有属性需要类型断言
  const recorderWithStream = recorder
  if (recorderWithStream.stream) {
    recorderWithStream.stream.getTracks().forEach((track) => track.stop())
  }
  audioLoading.value = false
  // 停止播放（如果有）
  recorder.stopPlay()
  // 重置状态
  isRecording.value = false
  recordingMode.value = ''
  stat.value = { duration: 0, fileSize: 0, vol: 0 }
  shouldCancel.value = false
  emit('recordingChange', false)
}

const exportMp3 = () => {
  const wavBlob = recorder.getWAVBlob()
  const reader = new FileReader()

  reader.onload = event => {
    const arrayBuffer = event.target?.result
    // 使用类型断言来访问 WavHeader
    const wavHeader = lamejs.WavHeader
    const wav = wavHeader.readHeader(new DataView(arrayBuffer))
    const samples = new Int16Array(arrayBuffer, wav.dataOffset, wav.dataLen / 2)

    const mp3Encoder = new lamejs.Mp3Encoder(wav.channels, OAI_SAMPLE_RATE, 128)
    const mp3Data = []
    const sampleBlockSize = 1152
    for (let i = 0; i < samples.length; i += sampleBlockSize) {
      const sampleChunk = samples.subarray(i, i + sampleBlockSize)
      const mp3buf = mp3Encoder.encodeBuffer(sampleChunk)
      if (mp3buf.length > 0) {
        mp3Data.push(new Int8Array(mp3buf))
      }
    }
    const mp3buf = mp3Encoder.flush()
    if (mp3buf.length > 0) {
      mp3Data.push(new Int8Array(mp3buf))
    }

    const mp3Blob = new Blob(mp3Data, { type: 'audio/mp3' })
    uploadMp3(mp3Blob)
  }

  reader.readAsArrayBuffer(wavBlob)
}

const downloadMp3 = (blob) => {
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.style.display = 'none'
  a.href = url
  a.download = 'recording.mp3'
  document.body.appendChild(a)
  a.click()
  URL.revokeObjectURL(url)
}

const { lang } = useI18n()
const uploadMp3 = (blob) => {
  // 停止上一个生成
  emit('stopGenerating')
  const formData = new FormData()
  formData.append('file', blob, 'recording.mp3')
  formData.append('voiceFormat', 'mp3')
  formData.append('language', lang || 'zh-cn')

  convertMp3ToText(formData)
    .then((response) => {
      console.log('Transcription success:', response)
      if (response.code === '0') {
        recordingMode.value = 'finish'

        if (!response.data?.result) {
          showToast('语音发送失败，请重试！')
        }

        recognizeText.value = response.data?.result || ''
        emit('recordingFinish', recognizeText.value)
        recordingMode.value = ''
      } else {
        recordingMode.value = ''
      }
    })
    .catch(error => {
      console.error('Transcription error:', error)
      recordingMode.value = 'cancel'
      resetRecorder()
    })
}
</script>

<style lang="less" scoped>
.voice-button {
  display: flex;
  justify-content: center;
  align-items: center;
  /* 原有样式保持不变 */
  touch-action: none;
  -webkit-touch-callout: none;
  /* 禁用iOS长按菜单 */
  user-select: none;
  /* 标准语法 */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE10+/Edge */
  -webkit-user-select: none;
  /* Safari/Chrome */
  transform: translate3d(0, 0, 0);
  will-change: transform;
  position: relative;
}

.voice-button.recording {
  background: linear-gradient(96deg, #7bb4a2 58%, #63bca0 100%);
}

.voice-button.cancel {
  background: linear-gradient(96deg, #ff8f85 58%, #fe5d4e 100%);
}

.button-text {
  font-size: 16px;
  font-family: PingFang SC, PingFang SC-500;
  font-weight: 500;
  text-align: center;
  color: #000;
  width: 100%;
}

/* 子元素继承禁用选中 */
.voice-button * {
  user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
}

/* 可选：禁用选中时的背景高亮 */
.voice-button::selection {
  background: transparent;
}
</style>
