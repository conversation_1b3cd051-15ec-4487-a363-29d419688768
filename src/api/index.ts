import axios from 'axios'
import type { AxiosProgressEvent } from 'axios'
import { post } from '@/utils/request'
import { getCommonHeader } from '@/utils/common'

interface ChatRequestParams {
  sid: string;
  q: string;
  selected_hotel?: string[];
  [key: string]: any;
}

// 获取历史记录：会话list
export function qryChatList<T>(data: any) {
  return post<T>({
    baseUrl: 'https://dtgw.qa.ly.com/deeptrip_qa5/business_travel',
    url: '/query_user_chat_list',
    data
  })
}

// 获取历史记录：单个会话中的 message list
export function qryChatHistory<T>(data: any) {
  return post<T>({
    baseUrl: 'https://dtgw.qa.ly.com/deeptrip_qa5/business_travel',
    url: '/query_chat_history',
    data
  })
}

export function delChatHistory<T>(data: any) {
  return post<T>({
    url: '/delete_chat',
    data
  })
}

// 发送聊天消息
export function sendChatMessage(
  params: ChatRequestParams,
  onDownloadProgress: (progressEvent: AxiosProgressEvent) => void,
  signal?: AbortSignal,
  customHeaders?: Record<string, string>
) {
  const headers = {
    Accept: 'text/event-stream',
    'Content-Type': 'application/json',
    ...getCommonHeader(),
    ...customHeaders
  }

  return axios({
    baseURL: import.meta.env.VITE_API_BASEURL,
    method: 'POST',
    url: '/api/TravelAssistant/Chat',
    data: { ...params },
    headers,
    responseType: 'text',
    withCredentials: true,
    signal,
    onDownloadProgress
  })
}

// 用户主动结束会话
export function stopStream<T>(data: any) {
  return post<T>({
    url: '/stop_stream',
    data
  })
}

export function convertMp3ToText<T>(data: FormData) {
  return post<T>({
    url: '/ten/sentenceRecognition',
    baseUrl: 'https://dtgw.qa.ly.com/deeptrip_qa5',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      uploadLimitSizeImgLikeWindToken: '7AF188EE729646539FC55C40588A8B07'
    }
  })
}
