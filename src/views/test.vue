<template>
    <button @click="showPopup">弹出选择人员框</button>
    <selectPassenger ref="selectPassengerRef"></selectPassenger>
</template>

<script setup>
import { ref } from 'vue'
import selectPassenger from '@/components/selectPassenger.vue';
  const selectPassengerRef = ref(null);
  const show = ref(false);
  function showPopup() {
    selectPassengerRef.value.open();
  }
</script>

<style scoped>

</style>