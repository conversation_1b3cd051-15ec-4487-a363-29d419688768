<template>
  <div @click="toggleThinking(message.id)" v-if="!message.content.includes('哈喽~')">
    <div class="think-title">
      <div class="title-content">
        <div class="content-left">
          <img class="loading-icon" v-if="message.status === 'writing' || message.status === 'loading'"
            src="@/assets/icons/loading.gif" />
          <img class="complete-icon" v-if="message.user_id || (message?.thinkingTime && message.thinkingTime > 0)"
            src="@/assets/icons/complete.png" />
          <span>程意商旅助手</span>
        </div>
        <span v-if="message?.thinkingTime && message.thinkingTime > 0">({{ `用时${message.thinkingTime} s` }})</span>
      </div>
      <img src="@/assets/icons/arrow-top.png" class="chevrondown-icon"
        :class="{ 'chevrondown-rotate': !message.thinking }" />
    </div>
  </div>
  <div class="think-box" :class="{ 'pb5': message.thinking }">
    <div :class="{ 'think-hidden': !message.thinking }" v-html="renderMarkdown(message.content || '')"></div>
    <div class="think-extra-msg" v-if="message.extra_msg && message.thinking">{{ message.extra_msg }}</div>
  </div>
</template>

<script setup>
const props = defineProps({
  message: Object,
  renderMarkdown: Function
})

const emit = defineEmits(['toggleThinking'])
const toggleThinking = (id) => {
  emit('toggleThinking', id)
}
</script>

<style lang="less" scoped>
.think-title {
  display: flex;
  align-items: center;
  padding: 8px 15px;

  .title-content {
    display: flex;
    align-items: center;

    .content-left {
      display: flex;
      align-items: center;

      .loading-icon {
        position: relative;
        top: -1px;
        width: 20px;
        margin-right: 5px;
      }

      .complete-icon {
        position: relative;
        top: -1px;
        width: 12px;
        margin-right: 5px;
      }

      span {
        font-weight: 400;
        font-size: 13px;
        color: #656565;
        line-height: 14px;
      }
    }

    span {
      font-weight: 400;
      font-size: 13px;
      color: #656565;
      line-height: 14px;
    }
  }


  .chevrondown-icon {
    width: 12px;
    margin-left: 5px;
    position: relative;
    top: -1px;

    &.chevrondown-rotate {
      transform: rotate(180deg);
    }
  }
}

.think-hidden {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  padding-bottom: 0px !important;
}

.think-extra-msg {
  color: rgba(27, 27, 27, 1);
  font-size: 13px;
  line-height: 22px;
  margin-top: 8px;
}
</style>

<style lang="less">
.think-box {
  padding-left: 38px;
}

.think-box h3,
.think-box h4 {
  position: relative;
  margin-bottom: 0;
  font-weight: 400;
}

.think-box h3 {
  color: #282828;
  font-size: 13px;
  line-height: 22px;
  margin-top: 8px;

  &:first-of-type {
    margin-top: 0;
  }
}

.think-box h4 {
  color: #656565;
  margin-top: 0;
  font-size: 13px;
  line-height: 22px;
}

.think-box h3::before {
  content: '';
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: #80aeff;
  position: absolute;
  top: 9px;
  left: -20px;
}

.think-box h4::before {
  content: '';
  width: 1px;
  background-color: #80aeff;
  position: absolute;
  top: -12px;
  left: -18px;
  bottom: -18px;
}

.think-box h4:last-child::before {
  display: none;
}
</style>
