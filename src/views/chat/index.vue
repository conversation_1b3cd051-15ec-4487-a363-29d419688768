<template>
  <div class="chat-page">
    <ChatHeader :class="{ shadow: chatContainerScrollTop > 0 }" />
    <div class="chat-container" ref="chatContainer" @scroll="chatContainerScroll">
      <template v-for="(message, msgIndex) in messages">
        <div v-if="message.role === 'user'" class="user-message">
          <div class="user-message-content" v-if="message.content" v-html="renderMarkdown(message.content)"></div>
        </div>
        <div v-else-if="message.msg_type !== 'image'" class="ai-message">
          <template v-if="message.llm_thinking_content">
            <div>
              <p v-if="message.status === 'error'">内测期间，算力有限，请稍后重试。</p>
              <ThinkingBox v-else :message="message" :renderMarkdown="renderMarkdown"
                @toggleThinking="toggleThinking(message.id)" />
            </div>
          </template>
          <template v-else-if="(typeof message.content === 'string') && message.content && message.content.trim()">
            <div class="answer-box">
              <div class="answer-content markdown-body">
                <template v-for="(item, compIndex) in getAnswerCompList(message)" :key="compIndex">
                  <component v-if="item.type === 'native'" :class="item.el.className" v-bind="item.dataset"
                    :is="item.el.tagName" v-html="item.el.innerHTML" />
                  <component v-if="item.type === 'custom' && item.component" v-bind="item.data" :is="item.component"
                    :sid="chatId" :msgId="message.msg_id" :message="message"
                    :latestMsg="msgIndex === messages.length - 1" />
                </template>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>
    <div class="chat-footer">
      <ChatInput ref="chatInputRef" :loading="loading" :inputPlaceholder="inputPlaceholder"
        @sendMessage="clickSendMessage" @stopGenerating="stopGenerating" @changeFocus="changeFocus" />
    </div>
    <div class="chat-cart" v-if="hasProductCard">
      <img src="@/assets/icons/cart.png" />
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, computed, watch, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { v4 as uuidv4 } from 'uuid'
import { useRoute, useRouter } from 'vue-router'
import { DEFAULT_HOLDER, CHAT_INIT_MSG } from '@/constants'
import { sendChatMessage, stopStream, qryChatHistory } from '@/api'
import useUserStore from '@/stores/user'
import useChatStore from '@/stores/chat'
import { getDefaultMsg } from '@/utils/chat'
import { renderMarkdown, getAnswerCompList } from '@/utils/renderMarkdown'
import { setGlobalStorage, getGlobalStorage } from '@/utils/storage'
import { eventBus, EVENTS } from '@/utils/eventBus'
import ChatHeader from './header.vue'
import ChatInput from '@/components/chatInput.vue'
import ThinkingBox from './thinking.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const chatStore = useChatStore()
const chatContainer = ref(null)
const isFocus = ref(false)
const loading = ref(false)
const reloadVisible = ref(false)
const chatInputRef = ref()
const initMsg = CHAT_INIT_MSG()
const inputPlaceholder = ref(DEFAULT_HOLDER())
const initText = ref('')
const inputText = ref('')
const isUserScrolled = ref(false)
const isAutoScrolling = ref(false)
const timer = ref(null)
const timeoutTime = 1000 * 60 * 2
const lastMsg = ref('')
const isShowFunction = ref(false)
// const memberId = computed(() => userStore.info?.memberId)
const memberId = computed(() => '312575606')
const chatId = computed(() => route.params.id || route.query.id)
const messages = computed(() => chatStore.getMessages(chatId.value))
// 会话接口的 signal controller，用来取消请求
const controller = ref(null)
const chatContainerScrollTop = ref(0)
const products = ['hotel', 'flight', 'train']

const hasProductCard = computed(() => {
  return messages.value.some(
    msg => msg.content?.includes('class="dt-json-container"') && products.some(product => msg.content?.includes(`"type": "${product}"`))
  )
})

const lastAnswerId = computed(() => {
  const lastAnswer = messages.value
    .slice()
    .reverse()
    .find(
      it =>
        it.role === 'assistant' &&
        it.llm_thinking_content === false &&
        it.msg_type === 'text' &&
        it.status !== 'loading' &&
        it.msg_id
    )
  return lastAnswer?.msg_id || ''
})

const lastQuestion = computed(() => {
  const question = messages.value
    .slice()
    .reverse()
    .find(
      it =>
        it.role === 'user' &&
        it.content &&
        it.llm_thinking_content === false &&
        it.msg_id,
    )

  return question?.content || ''
})

const clickSendMessage = (searchText) => {
  sendMessage(searchText)
}

// 发送消息
const sendMessage = async (text, isResume) => {
  if (!text?.trim() || loading.value) return
  isFocus.value = false

  // 更改历史栏的conversation_name
  if (messages.value[0] && messages.value[0].status === 'init') {
    chatStore.updateChat(chatId.value, {
      conversation_name: text
    })
  }

  controller.value = window['AbortController'] ? new window['AbortController']() : null

  let { userMessage, thinkMessage, aiMessage } = getDefaultMsg(chatId.value, uuidv4(), text)

  if (isResume) {
    const lastMessage = chatStore.getLastMessage(chatId.value) || {}
    const msgs = getDefaultMsg(chatId.value, lastMessage.query_msg_id)
    if (lastMessage.role === 'user' || lastMessage.status === 'init') {
      thinkMessage = msgs.thinkMessage
      aiMessage = msgs.aiMessage
      chatStore.addMessage(chatId.value, thinkMessage)
      chatStore.addMessage(chatId.value, aiMessage)
    } else if (lastMessage.role === 'assistant') {
      thinkMessage = chatStore.getLastThinkMessage(chatId.value) || msgs.thinkMessage
      if (lastMessage?.llm_thinking_content) {
        aiMessage = getDefaultMsg(chatId.value, thinkMessage.query_msg_id).aiMessage
        chatStore.addMessage(chatId.value, aiMessage)
      } else {
        aiMessage = chatStore.getLastAiMessage(chatId.value)
      }
    }
  } else {
    chatStore.addMessage(chatId.value, userMessage)
    chatStore.addMessage(chatId.value, thinkMessage)
    chatStore.addMessage(chatId.value, aiMessage)
  }

  loading.value = true

  await scrollToBottom()

  // 重置
  isUserScrolled.value = false
  isAutoScrolling.value = false

  const startTime = new Date().getTime()
  let accumulatedContentThinking = ''
  let accumulatedContentAnswer = ''
  let lastAnswerType = ''
  let lastProcessedLength = 0 // 添加变量记录上次处理的位置

  try {
    inputText.value = text
    await sendChatMessage(
      {
        url: isResume ? 'resume_stream' : 'hotel_chat',
        sid: chatId.value,
        q: text,
        ...route.query,
        lastAnswerId: isResume ? lastAnswerId.value : ''
      },
      async (progressEvent) => {
        if (timer.value !== null) {
          clearTimeout(timer.value)
          timer.value = null
        }

        timer.value = setTimeout(() => {
          stopGenerating('timeout')
          timer.value = null
        }, timeoutTime)

        const responseText = progressEvent.event.target.responseText
        if (!responseText) return

        if (!responseText.trim().endsWith('}')) return

        lastMsg.value = JSON.stringify(responseText)

        // 只处理新增的内容
        const newContent = responseText.substring(lastProcessedLength)
        lastProcessedLength = responseText.length

        const lines = newContent.split('\n')
        if (lines.length === 1 && lines[0] === 'null') {
          showToast('系统繁忙，请稍后再试')
          return
        }

        for (let i = 0; i < lines.length; i++) {
          let line = lines[i]

          try {
            if (!line.trim()) continue
            // ?? 这里 replace 了两次 data:  可能是数据不太对
            line = line.trim()
            line = line.replace('data:', '')
            line = line.replace('data:', '')
            const data = JSON.parse(line)

            if (data.type === 'finsh' || data.type === 'exp_friend_alert') {
              chatStore.updateMessage(chatId.value, aiMessage.id, {
                status: 'done',
                ans_by: data.ans_by,
                msg_id: data.ans_msg_id,
                id: data.ans_msg_id
              })

              chatStore.updateMessage(chatId.value, thinkMessage.id, {
                ans_by: data.ans_by,
                thinking: false,
                status: 'done'
              })

              loading.value = false

              if (data.type === 'exp_friend_alert') {
                showToast(data.text)
              }

              break
            }

            if (data.type === 'notice') {
              chatStore.updateMessage(chatId.value, thinkMessage.id, {
                extra_msg: data.text
              })
            }

            if (data.type === 'thinking') {
              if (data.text !== '\n\n') {
                lastAnswerType = 'thinking'
              }

              thinkMessage.status = 'writing'
              accumulatedContentThinking += data.text
              chatStore.updateMessage(chatId.value, thinkMessage.id, {
                content: accumulatedContentThinking,
                thinking: true
              })

              isAutoScrolling.value = true

              if (!isUserScrolled.value) {
                scrollToBottom()
                setTimeout(() => {
                  isAutoScrolling.value = false
                }, 100)
              }
            }

            if (data.type === 'answer') {
              if (data.text === 'function') {
                isShowFunction.value = !isShowFunction.value
              }

              // 正式答案开始了
              if (data.text !== '\n\n' && data.text !== '' && lastAnswerType === 'thinking') {
                chatStore.updateMessage(chatId.value, thinkMessage.id, {
                  thinking: false,
                  status: 'done'
                })

                const endTime = new Date().getTime()
                const duration = Math.max(Number(((endTime - startTime) / 1000).toFixed(1)), 2.5)
                chatStore.updateMessage(chatId.value, thinkMessage.id, {
                  thinkingTime: duration
                })

                // 开始生成答案，需要改成自动滚动
                isUserScrolled.value = false
              }

              if (data.text !== '\n\n') {
                lastAnswerType = 'answer'
              }

              accumulatedContentAnswer += data.text
              chatStore.updateMessage(chatId.value, aiMessage.id, {
                content: accumulatedContentAnswer,
                status: 'writing'
              })

              isAutoScrolling.value = true
              if (!isUserScrolled.value) {
                scrollToBottom()
                setTimeout(() => {
                  isAutoScrolling.value = false
                }, 100)
              }
            }

            if (data.type === 'sight' && data.text === '1') {
              chatStore.updateMessage(chatId.value, aiMessage.id, {
                has_sight: true
              })

              scrollToBottom()
            }
          } catch (e) {
            console.error('解析消息失败: ', e)

            if (timer.value !== null) {
              clearTimeout(timer.value)
              timer.value = null
            }
          }
        }
      },
      controller.value?.signal
    )
  } catch (error) {
    console.error(new Date().getTime() + '会话接口错误: ', error)
    if (timer.value !== null) {
      clearTimeout(timer.value)
      timer.value = null
    }

    // 处理 AbortError
    if (error.name === 'CanceledError') {
      if (error?.config?.signal?.reason === 'timeout') {
        chatStore.updateMessage(chatId.value, thinkMessage.id, {
          content: '[请求超时]',
          status: 'done'
        })
        return
      }
    } else if (error.code === 'ERR_NETWORK') {
      // 重新连接
      fetchChatHistory()
    } else {
      chatStore.updateMessage(chatId.value, thinkMessage.id, {
        status: 'error'
      })
      if (error.status === 429) {
        // 处理限流
        showToast('服务器繁忙，请稍后再试，感谢您的耐心！')
      }
    }
  } finally {
    loading.value = false
    controller.value = null
    if (timer.value !== null) {
      clearTimeout(timer.value)
      timer.value = null
    }
    await scrollToBottom()
  }
}

// 停止生成
const stopGenerating = (reason) => {
  if (controller.value) {
    const thinkMessage = chatStore.getLastThinkMessage(chatId.value)
    const lastAiMessage = chatStore.getLastAiMessage(chatId.value)

    chatStore.updateMessage(chatId.value, thinkMessage?.id, {
      status: 'stop'
    })

    chatStore.updateMessage(chatId.value, lastAiMessage.id, {
      content: '[已停止生成]',
      status: 'stop'
    })

    const params = {
      lastAnswerId: lastAnswerId.value,
      sid: chatId.value
    }

    stopStream(params)

    controller.value.abort(reason)
    controller.value = null
    if (timer.value !== null) {
      clearTimeout(timer.value)
      timer.value = null
    }
  }
}

const changeFocus = (val) => {
  isFocus.value = val
}

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick()
  setTimeout(() => {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight
    }
  }, 20)
}

const reload = () => {
  reloadVisible.value = false
  fetchChatHistory()
}

const resetChat = () => {
  if (timer.value !== null) {
    clearTimeout(timer.value)
    timer.value = null
  }

  isShowFunction.value = false
  inputPlaceholder.value = DEFAULT_HOLDER()
  chatInputRef.value?.clearInputMessage()
}

const reloadChat = async () => {
  const list = await chatStore.initChatData({
    user_id: memberId.value.toString() || ''
  })
  const id = list && list[0] && list[0].conversation_id

  router.replace({ name: 'Chat', params: { id: id || uuidv4() } })
}

async function fetchChatHistory() {
  if (!chatId.value) {
    reloadChat()
    return
  }

  const { code, data, msg } = await qryChatHistory({
    sid: chatId.value,
    page_size: 500
  })

  if (code !== 0) {
    showToast(msg)
    return
  }

  chatStore.updateMessages(chatId.value, data.list)

  // 添加欢迎消息
  if (messages.value.length === 0) {
    resetChat()
    const id = uuidv4()
    chatStore.addMessage(chatId.value, {
      id,
      msg_id: id,
      conversation_id: chatId.value,
      role: 'assistant',
      content: initMsg,
      status: 'init'
    })
    // 仅自己预订权限时，发送一条本人预订消息
    if (userStore.bookRoleType === 1) {
      sendMessage('本人预订')
    }
    return
  }

  if (data.completed === false) {
    loading.value = false
    // 如果会话未结束，继续生成
    sendMessage(lastQuestion.value, true)
  }

  loading.value = !data.completed
}

const chatContainerScroll = () => {
  if (!isAutoScrolling.value) {
    isUserScrolled.value = true
  }

  if (chatContainer.value) {
    chatContainerScrollTop.value = chatContainer.value.scrollTop

    if (Math.ceil(chatContainer.value.scrollTop + chatContainer.value.clientHeight) >= chatContainer.value.scrollHeight) {
      if (isUserScrolled.value) {
        isUserScrolled.value = false
      }
    }
  }
}

// 切换thinking
const toggleThinking = (id) => {
  const message = messages.value.find(message => message.id === id)

  if (message) {
    chatStore.updateMessage(chatId.value, id, {
      thinking: !message.thinking
    })
  }
}

// 初始化聊天
async function initChat() {
  if (memberId.value) {
    // 如果登录了则去拿历史聊天
    await chatStore.initChatData({
      user_id: memberId.value.toString() || ''
    })
  }

  await fetchChatHistory()

  const searchText = getGlobalStorage('searchText') || initText.value
  if (searchText) {
    setGlobalStorage('searchText', '')
    initText.value = ''

    sendMessage(searchText)
  }
}

let networkTimeout = null
const offlineTime = ref(0) // 断网时间
const updateNetworkInfo = (onLine) => {
  const timeout = 1000 * 20

  if (networkTimeout) {
    clearTimeout(networkTimeout)
    networkTimeout = null
  }

  if (!onLine) {
    offlineTime.value = new Date().getTime()
    controller?.value && controller.value.abort()
    networkTimeout = setTimeout(() => {
      showToast('网络异常，请检查网络连接')
    }, timeout)
    return
  }

  if (offlineTime.value === 0) return

  const now = new Date().getTime()
  const offline = now - offlineTime.value

  // 判断网络断开时间
  if (offline < timeout) {
    const lastUserMessage = messages.value
      .slice()
      .reverse()
      .find(item => item.role === 'user' && item.content && !item.llm_thinking_content)
    sendMessage(lastUserMessage?.content, true)
    offlineTime.value = 0
    return
  }

  if (reloadVisible.value) return
  reloadVisible.value = true
  offlineTime.value = 0
}

const handleVisibilityChange = () => {
  const isPageVisible = document.visibilityState === 'visible'

  // if (isPageVisible) {
  //   fetchChatHistory()
  //   return
  // }

  // controller?.value && controller.value.abort('')
  // controller.value = null
}

const handleSendChatMsg = (msg) => {
  sendMessage(msg)
}

// 用户id有值以后，初始化聊天
watch(() => memberId.value, async (id) => id && initChat(), {
  immediate: true
})

// 切换会话后重新加载历史记录
watch(() => chatId.value, async newId => newId ? fetchChatHistory() : reloadChat())

onMounted(async () => {
  initText.value = route.query.initText || ''

  eventBus.on(EVENTS.SEND_CHAT_MSG, handleSendChatMsg)

  window.addEventListener('online', () => updateNetworkInfo(true))

  window.addEventListener('offline', () => updateNetworkInfo(false))

  window.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  if (networkTimeout !== null) {
    clearTimeout(networkTimeout)
    networkTimeout = null
  }

  eventBus.off(EVENTS.SEND_CHAT_MSG, handleSendChatMsg)
  window.removeEventListener('offline', () => { })
  window.removeEventListener('offline', () => { })
  window.removeEventListener('visibilitychange', handleVisibilityChange)
  controller?.value && controller.value.abort()
})
</script>

<style scoped lang="less">
@import "./index.less";
</style>
