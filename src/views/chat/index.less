.chat-page {
  position: relative;
  box-sizing: border-box;
  background: #f5f7fa;
  height: 100vh;
  padding-top: 50px;
  padding-bottom: 80px;
}

.shadow {
  box-shadow: 0px 0px 6px 0px #dee6ef;
}

.chat-container {
  height: calc(100vh - 130px);
  box-sizing: border-box;
  padding-bottom: 20px;
  overflow-y: auto;
}

.user-message {
  display: flex;
  justify-content: flex-end;
  margin: 10px 0;
  padding: 0 15px;
}

.user-message-content {
  font-weight: 400;
  font-size: 16px;
  color: #fff;
  line-height: 24px;
  padding: 7px 14px;
  background: linear-gradient(90deg, #4a75ed 0%, #0054ea 100%);
  box-shadow: 0px 2px 2px 1px rgba(0, 0, 0, 0.01);
  border-radius: 15px 15px 4px 15px;

  &:deep(p) {
    margin-block-start: 0;
    margin-block-end: 0;
  }
}

.answer-box {
  padding: 0 15px;
}

.chat-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.chat-cart {
  position: fixed;
  bottom: 100px;
  right: 15px;
  z-index: 101;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #4a75ed 0%, #0054ea 100%);
  box-shadow: 0px 2px 9px 0px rgba(13, 90, 235, 0.3);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;

  >img {
    width: 38px;
  }
}