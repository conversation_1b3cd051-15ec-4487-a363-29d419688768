<template>
  <div class="chat-header">
    <div class="chat-header-back">
      <img src="@/assets/icons/back.png" />
    </div>
    <div class="chat-header-tit">程意商旅助手</div>
    <div class="chat-header-menu">
      <img src="@/assets/icons/preference.png" @click="goMyPreferencePage" />
      <img src="@/assets/icons/history.png" />
    </div>
  </div>
</template>

<script setup>
  function goMyPreferencePage() {
      window.location.href="/newapp/pages/my/preferences"
  }
</script>

<style scoped lang="less">
.chat-header {
  z-index: 99;
  height: 50px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.chat-header-back,
.chat-header-menu {
  width: 100px;
  height: 50px;
  display: flex;
  align-items: center;

  >img {
    height: 18px;
  }
}

.chat-header-back {
  justify-content: flex-start;

  >img {
    margin-right: 20px;
  }
}

.chat-header-menu {
  justify-content: flex-end;

  >img {
    margin-left: 20px;
  }
}

.chat-header-tit {
  flex: 1;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  color: #000;
}
</style>
