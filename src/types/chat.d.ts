interface ChatHistoryItem {
  user_id?: string; // 用户id
  conversation_id: string; // 会话id
  id?: string; // 会话id
  last_time?: number; // 该会话最后一次用户消息时间
  first_time?: number; // 该会话首次用户消息时间
  conversation_name: string; // 会话名称
  status?: string;
}

interface Message {
  role: string;
  content: any;
  conversation_id: string;
  thinking_content?: string;
  user_id?: string;
  llm_thinking_content?: boolean; // 大模型thinking状态
  send_time?: number;
  msg_id?: string;
  id?: string;
  status?: string;
  thinkingTime?: number;
  thinking?: boolean;
  extra_msg?: string; // 额外信息
  need_recommend?: boolean; // 是否有推荐
  msgType?: string;
  has_sight?: boolean; // 是否旅行
  msg_type?: string; // 消息类型
  imgUrl?: string;
  query_msg_id?: string; // 对话组id
  is_trip?: boolean; // 是否是行程
  extra_type?: string; // 额外类型
  progress?: number; // 进度
  refer_msg_id?: string; // 引用消息id
  more_site_guide?: boolean; // 更多景点介绍
  [key: string]: any; // 其它任意属性
}
