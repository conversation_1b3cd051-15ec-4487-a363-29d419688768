import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { storeToRefs } from 'pinia'
import useUsersStore from '@/stores/user'
import { getLocalStorage, setLocalStorage } from '@/utils/local'
import { getToken } from '@/utils/token'

const routes: RouteRecordRaw[] = [
  {
    path: '/:id?',
    name: 'Chat',
    component: () => import('@/views/chat/index.vue'),
    meta: {
      requiresAuth: true
    }
  },
  {
    path: '/test',
    name: 'test',
    component: () => import('@/views/test.vue'),
    meta: {
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes
})

// 路由跳转
function toNextPage(to: RouteLocationNormalized, next: NavigationGuardNext) {
  // 判断是否为登录页，或在白名单内
  if (!to.meta.requiresAuth) {
    next()
  } else {
    window.location.href = '/newapp/pages/login/login'
  }
}

// 验证token有效性及是否过期
async function checkToken(token: string, to: RouteLocationNormalized, next: NavigationGuardNext) {
  if (token) {
    const userStore = useUsersStore()
    const { configLasts, token } = storeToRefs(userStore)
    const LocalStorage = getLocalStorage()
    const { EmployeeConfigLastTs } = LocalStorage
    let ts = Math.abs(Date.now() - EmployeeConfigLastTs)
    // 是否超过60s
    if (ts < 60 * 1000) {
      await routeJump(to, next)
    } else {
      // 调用接口验证token
      let result = await userStore.getUserInfo()
      console.log('result', result)

      if (result.success) {
        configLasts.value = result.timeStamp || 0
        userStore.changeState(result.result)

        // 更新缓存
        LocalStorage.EmployeeConfig = result.result
        LocalStorage.EmployeeConfigLastTs = result.timeStamp
        LocalStorage.EmployeeToken = getToken()

        setLocalStorage(LocalStorage)

        await routeJump(to, next)
      } else {
        token.value = ''
        toNextPage(to, next)
      }
    }
  } else {
    toNextPage(to, next)
  }
}

// 跳转，加载动态路由
async function routeJump(to: RouteLocationNormalized, next: NavigationGuardNext) {
  next()
}

router.beforeEach(async (_to, _from, next) => {
  // 获取登录token
  const userStore = useUsersStore()
  const { token } = storeToRefs(userStore)

  if (_to.meta.title) {
    document.title = _to.meta.title as string
  }

  if (!token.value) {
    let _tempToken = getToken()
    userStore.setToken(_tempToken)
  }

  // 校验token
  await checkToken(userStore.getToken, _to, next)
})

export default router
