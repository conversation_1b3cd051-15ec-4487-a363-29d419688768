import { defineStore } from 'pinia'
import { ref } from 'vue'
import { qryChatList } from '@/api'
import { CHAT_INIT_MSG } from '@/constants'
import { showToast } from 'vant'

interface ChatHistory {
  list: ChatHistoryItem[];
  total: number;
}

interface MessageMap {
  [conversation_id: string]: Message[];
}

const useChatStore = defineStore('chat', () => {
  const chatData = ref<ChatHistoryItem[]>([])
  const messageMap = ref<MessageMap>({})

  // 初始化加载数据
  const initChatData = async (params: any) => {
    try {
      const { code, msg, data } = await qryChatList<ChatHistory>(params)
      if (code !== 0) {
        showToast(msg)
        return
      }
      chatData.value = data.list
      return data.list
    } catch (error) {
      console.error('Failed to load chat data:', error)
      showToast('加载聊天记录失败')
    }
  }

  // 获取指定聊天消息
  const getMessages = (chatId: string) => {
    return messageMap.value[chatId] || []
  }

  // 更新指定聊天消息
  const updateMessages = (chatId: string, messages: Message[]) => {
    messageMap.value[chatId] = messages

    // 新增会话
    if (!chatData.value.some((chat) => chat.conversation_id === chatId)) {
      chatData.value.unshift({
        conversation_id: chatId,
        conversation_name: CHAT_INIT_MSG(),
        status: 'init',
      })
    }
  }

  // 添加消息到指定聊天
  const addMessage = (chatId: string, message: Message) => {
    const existing = messageMap.value[chatId]

    if (!existing) {
      messageMap.value[chatId] = [message]
    } else {
      existing.push(message)
    }
  }

  // 更新指定聊天中的消息
  const updateMessage = (chatId: string, messageId: string, updates: Partial<Message>) => {
    const messages = messageMap.value[chatId]
    if (!messages) return

    const messageIndex = messages.findIndex((m) => m.id === messageId)
    if (messageIndex !== -1) {
      const updatedMessage = { ...messages[messageIndex], ...updates }
      messages.splice(messageIndex, 1, updatedMessage)
    }
  }

  // 更新指定聊天
  const updateChat = (chatId: string, updates: Partial<ChatHistoryItem>) => {
    const chatIndex = chatData.value.findIndex((chat) => chat.conversation_id === chatId)
    if (chatIndex === -1) return
    chatData.value[chatIndex] = {
      ...chatData.value[chatIndex],
      ...updates,
    }
  }

  // 清除指定聊天记录
  const clearChat = (chatId: string) => {
    messageMap.value[chatId] = []
  }

  // 清除所有聊天记录
  const clearAllChats = () => {
    chatData.value = []
    messageMap.value = {}
  }

  // 获取所有聊天
  const getAllChats = () => {
    return chatData.value
  }

  // 获取最后一次thinking的MsgId
  const getLastThinkingMsgId = (chatId: string) => {
    const messages = messageMap.value[chatId]
    if (!messages?.length) return null

    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === 'user') {
        return null
      }
      if (
        messages[i].role === 'assistant' &&
        messages[i].llm_thinking_content &&
        messages[i].msg_id
      ) {
        return messages[i].msg_id
      }
    }

    return null
  }

  const getLastThinkMessage = (chatId: string) => {
    const messages = messageMap.value[chatId]
    if (!messages?.length) return null

    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === 'assistant' && messages[i].llm_thinking_content) {
        return messages[i]
      }
    }
    return null
  }

  const getLastAiMessage = (chatId: string) => {
    const messages = messageMap.value[chatId]
    if (!messages?.length) return null

    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === 'assistant' && !messages[i].llm_thinking_content) {
        return messages[i]
      }
    }
    return null
  }

  const getLastMessage = (chatId: string) => {
    const messages = messageMap.value[chatId]
    if (!messages?.length) return null

    return messages[messages.length - 1]
  }

  // 判断消息是否存在
  const hasMessage = (chatId: string, msgId: string) => {
    const messages = getMessages(chatId)
    return messages.some((message) => message.msg_id === msgId)
  }

  const getMessageIndex = (chatId: string, msgId: string) => {
    const messages = getMessages(chatId)
    return messages.findIndex((message) => message.msg_id === msgId)
  }

  return {
    chatData,
    initChatData,
    getMessages,
    updateMessages,
    addMessage,
    updateMessage,
    clearChat,
    clearAllChats,
    getAllChats,
    updateChat,
    getLastThinkingMsgId,
    getLastThinkMessage,
    getLastAiMessage,
    getLastMessage,
    hasMessage,
    getMessageIndex
  }
})

export default useChatStore