import cookies from 'js-cookie'
import { LocalStorageService } from '@/utils/local'

export const getToken = () => {
  let LocalStorage = LocalStorageService.get('local_storage')
  let tokenStr = LocalStorage.EmployeeToken || ''

  // 获取cookie
  if (!tokenStr) {
    tokenStr = cookies.get('spec_dtg_uid')
  }

  // 本地化取配置文件的token用
  if (import.meta.env.VITE_TEST_TOKEN) {
    tokenStr = import.meta.env.VITE_TEST_TOKEN
  }

  return tokenStr
}
