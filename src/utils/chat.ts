import { v4 as uuidv4 } from 'uuid'

export function getDefaultMsg(conversation_id: string, query_msg_id: string, text: string) {
  const userMessage = {
    id: query_msg_id,
    query_msg_id,
    conversation_id,
    content: text,
    role: 'user',
    status: 'done'
  }

  const thinkMessage = {
    id: uuidv4(),
    query_msg_id,
    conversation_id,
    role: 'assistant',
    content: '',
    thinkingTime: 0,
    llm_thinking_content: true,
    status: 'loading',
    thinking: true
  }

  const aiMessage = {
    id: uuidv4(),
    conversation_id,
    query_msg_id,
    role: 'assistant',
    content: '',
    thinkingTime: 0,
    llm_thinking_content: false,
    status: 'loading',
    thinking: true
  }

  return {
    userMessage,
    thinkMessage,
    aiMessage
  }
}
