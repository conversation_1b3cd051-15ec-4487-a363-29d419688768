import { marked } from 'marked'
import { defineAsyncComponent } from 'vue'

const chatContentComps = {
  travelUser: defineAsyncComponent(() => import('@/components/chatContent/travelUser.vue')),
  travelApplyNo: defineAsyncComponent(() => import('@/components/chatContent/travelApplyNo.vue')),
  hotel: defineAsyncComponent(() => import('@/components/chatContent/hotelCard.vue')),
  flight: defineAsyncComponent(() => import('@/components/chatContent/flightCard.vue')),
  train: defineAsyncComponent(() => import('@/components/chatContent/trainCard.vue'))
}

export function getAnswerCompList(message, disable) {
  const els = getAnswerElList(message, disable)

  return els.map((el) => {
    const cls = el.className

    if (cls === 'dt-json-container') {
      const data = getElData(el)
      return {
        type: 'custom',
        component: chatContentComps[data.type],
        data
      }
    }

    const child = el.querySelector('.dt-json-container')

    if (child) {
      const data = getElData(child)
      // console.log(data)
      return {
        type: 'custom',
        component: chatContentComps[data.type],
        data
      }
    }

    const originDataset = { ...el.dataset }
    const dataset = {}
    for (const key in originDataset) {
      dataset['data-' + key] = originDataset[key]
    }

    return { el, type: 'native', dataset }
  })
}

function getAnswerElList(message, disable) {
  const answerHtml = renderMarkdown(message.content, disable)
  const div = document.createElement('div')
  div.innerHTML = answerHtml

  if (div.children.length === 0) {
    return []
  }

  if (div.children.length === 1 && div.children[0].tagName === 'ANSWER') {
    return Array.from(div.children[0].children)
  }

  return Array.from(div.children)
}

export const renderMarkdown = (content, disable) => {
  if (!content) return ''

  // 去掉酒店序号
  content = content.replace(/([\d+⓪①②③④⑤⑥⑦⑧⑨⑩⑪]\.?\s?)?<a class="hotel-link-name"/g, () => {
    return `<a class="hotel-link-name"`
  })

  if (disable) {
    content = content.replace(/<a class="hotel-link-name"/g, () => {
      return `<a class="hotel-link-name disable-link"`
    })
    content = content.replace(/<a class="sight-link-name"/g, () => {
      return `<a class="sight-link-name disable-link"`
    })
    content = content.replace(/<a class="train-link-name"/g, () => {
      return `<a class="train-link-name disable-link"`
    })
    content = content.replace(/<a class="flight-link-name"/g, () => {
      return `<a class="flight-link-name disable-link"`
    })
    content = content.replace(/<a class="trainFlight-link-name"/g, () => {
      return `<a class="trainFlight-link-name disable-link"`
    })
  }

  const lines = content.split('\n')
  const newContent = []

  for (let i = 0; i < lines.length; i++) {
    const currentLine = lines[i].trim() // 去除首尾空格
    const nextLine = lines[i + 1]?.trim() || '' // 获取下一行（如果存在）

    // 检查当前行是否以 "|" 结尾，或者下一行是否以 "|" 开头
    const endsWithPipe = currentLine.endsWith('|')
    const startsWithPipe = nextLine.startsWith('|')

    // 如果当前行和下一行都不包含 "|"，并且当前行非空
    if (!endsWithPipe && !startsWithPipe && currentLine !== '') {
      newContent.push(currentLine + '\n\n') // 添加两个换行符
    } else {
      // 如果当前行为空，或者需要保持单个换行符
      newContent.push(currentLine + '\n')
    }
  }

  // 合并处理后的行
  let newContentStr = newContent.join('')
  // 把</div>**t替换为</div>，避免markdown解析为strong标签
  newContentStr = newContentStr.replace(/<\/div>\*\*/g, '</div>')

  return marked(newContentStr, { async: false })
}

function getElData(el) {
  const dataset = el.dataset

  if (dataset.json) {
    return JSON.parse(dataset.json)
  }

  return {}
}
