import type { AxiosProgressEvent, AxiosResponse, GenericAbortSignal } from 'axios'
import request from './axios'
import { sendErrLog } from './log'
import { login } from '@/service/auth'
import { getCommonHeader } from '@/utils/common'
import { showToast } from 'vant'

export interface HttpOption {
  url: string;
  data?: any;
  method?: string;
  baseUrl?: string;
  headers?: any;
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void;
  signal?: GenericAbortSignal;
  beforeRequest?: () => void;
  afterRequest?: (errorMessage?: string) => void;
  ignoreLogin?: boolean;
}

export interface Response<T = any> {
  data: T;
  message: string | null;
  msg: string;
  status: string | number;
  code?: string | number;
  success?: boolean;
  timeStamp?: number;
  result: T;
}

function http<T = any>({
  url,
  data,
  method,
  headers,
  onDownloadProgress,
  signal,
  beforeRequest,
  afterRequest,
  baseUrl,
  ignoreLogin
}: HttpOption) {
  const successHandler = (res: AxiosResponse<Response<T>>) => {
    if (res.data.status === 'Success' || res.data.code == 0 || res.data.status == 0 || res.data.code == '200008') {
      return res.data
    }
    // 登录验证失败或已过期
    if (['200010', '200009'].includes(res.data?.code?.toString() || '') && !ignoreLogin) {
      login()
    }
    return Promise.reject(res.data)
  }

  const failHandler = (error: Response<Error>) => {
    console.log(error)
    if (error?.status === 429) {
      showToast('服务器繁忙，请稍后再试，感谢您的耐心！')
    }
    afterRequest?.(error?.message || '')
    sendErrLog(
      JSON.stringify({
        env: import.meta.env.MODE,
        url,
        host: window.location.host,
        data,
        error,
        errMsg: error?.message || '',
      }),
    )
    throw new Error(error?.message || 'Error')
  }

  beforeRequest?.()

  method = method || 'GET'

  const params = Object.assign(typeof data === 'function' ? data() : (data ?? {}), {})
  if (baseUrl) {
    request.defaults.baseURL = baseUrl
  } else {
    request.defaults.baseURL = import.meta.env.VITE_API_BASEURL
  }
  request.defaults.withCredentials = true
  headers = {
    ...getCommonHeader(),
    ...headers
  }

  if (method === 'DELETE') {
    return request
      .delete(url, { data: params, headers, signal, onDownloadProgress })
      .then(successHandler, failHandler)
  }

  return method === 'GET'
    ? request
      .get(url, { params, headers, signal, onDownloadProgress, withCredentials: false })
      .then(successHandler, failHandler)
    : request
      .post(url, params, { headers, signal, onDownloadProgress })
      .then(successHandler, failHandler)
}

export function get<T = any>({
  url,
  data,
  method = 'GET',
  headers,
  onDownloadProgress,
  signal,
  beforeRequest,
  afterRequest,
  baseUrl
}: HttpOption): Promise<Response<T>> {
  return http<T>({
    url,
    method,
    data,
    onDownloadProgress,
    signal,
    beforeRequest,
    afterRequest,
    headers,
    baseUrl
  })
}

export function post<T = any>({
  url,
  data,
  method = 'POST',
  headers,
  onDownloadProgress,
  signal,
  beforeRequest,
  afterRequest,
  baseUrl,
  ignoreLogin
}: HttpOption): Promise<Response<T>> {
  return http<T>({
    url,
    method,
    data,
    headers,
    onDownloadProgress,
    signal,
    beforeRequest,
    afterRequest,
    baseUrl,
    ignoreLogin
  })
}
