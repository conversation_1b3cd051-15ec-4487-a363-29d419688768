import axios from 'axios'
import type { AxiosResponse } from 'axios'

const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASEURL,
  withCredentials: true
})

service.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error.response)
  }
)

service.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    if (response.status === 200) return response
    throw new Error(response.status.toString())
  },
  (error) => {
    return Promise.reject(error)
  }
)

export default service
