import type { PluginOption, ConfigEnv } from 'vite'
import Vue from '@vitejs/plugin-vue'
import type { AcceptedPlugin } from 'postcss'
import Autoprefixer from 'autoprefixer'
import PostcssPxtorem from 'postcss-pxtorem'
import { conifgCompressDist } from './compressDist'

export function createVitePlugins({ command, mode }: ConfigEnv): PluginOption[] {
  const isBuild = command === 'build'

  const plugins: PluginOption[] = [Vue()]

  if (isBuild) {
    plugins.push(conifgCompressDist())
  }

  return plugins
}

export function createPostCSSPlugins(): AcceptedPlugin[] {
  const plugins: AcceptedPlugin[] = []

  plugins.push(Autoprefixer())

  plugins.push(
    PostcssPxtorem({
      rootValue: 37.5,
      unitPrecision: 5,
      propList: ['*', '!border']
    })
  )

  return plugins
}