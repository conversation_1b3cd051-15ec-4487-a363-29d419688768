import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import type { ConfigEnv, UserConfig } from 'vite'
import { createVitePlugins, createPostCSSPlugins } from './config/vite/plugins'

const CWD = process.cwd()

export default defineConfig(({ command, mode }: ConfigEnv): UserConfig => {
  const { VITE_BASE_URL } = loadEnv(mode, CWD)

  return {
    base: VITE_BASE_URL,
    plugins: createVitePlugins({ command, mode }),
    server: {
      host: '0.0.0.0',
      port: 5173
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    css: {
      postcss: {
        plugins: createPostCSSPlugins()
      }
    }
  }
})
